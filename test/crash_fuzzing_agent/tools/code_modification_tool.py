"""
Code Modification Tool for applying improvements to the query generator.
"""

import os
import re
import json
from typing import Dict, List, Any
from agents import function_tool

@function_tool
def apply_query_generator_improvements(
    improvement_suggestions: str,
    target_file_path: str = "test/crash_fuzzing_agent/tools/random_query_generator_tool.py"
) -> str:
    """
    Apply suggested improvements to the query generator code.
    
    Args:
        improvement_suggestions: JSON string containing suggested modifications
        target_file_path: Path to the query generator file to modify
    
    Returns:
        JSON string containing the results of applying modifications
    """
    try:
        # Parse the improvement suggestions
        suggestions = json.loads(improvement_suggestions)
        
        if "error" in suggestions:
            return json.dumps({
                "success": False,
                "error": "Cannot apply improvements due to analysis error",
                "modifications_applied": 0,
                "details": suggestions["error"]
            })
        
        # Read the current file content
        if not os.path.exists(target_file_path):
            return json.dumps({
                "success": False,
                "error": f"Target file not found: {target_file_path}",
                "modifications_applied": 0
            })
        
        with open(target_file_path, 'r') as f:
            original_content = f.read()
        
        modified_content = original_content
        modifications_applied = 0
        modification_details = []
        
        # Apply each suggested modification
        suggested_mods = suggestions.get("suggested_modifications", [])
        
        for mod in suggested_mods:
            location = mod.get("location", "")
            change_type = mod.get("change_type", "")
            description = mod.get("description", "")
            code_snippet = mod.get("code_snippet", "")
            
            try:
                if change_type == "modify" and "safe_query_probability" in location:
                    # Modify the safe_query_probability value
                    pattern = r'self\.safe_query_probability\s*=\s*[\d.]+.*?(?=\s*#|$)'
                    if re.search(pattern, modified_content):
                        modified_content = re.sub(pattern, code_snippet.strip(), modified_content)
                        modifications_applied += 1
                        modification_details.append(f"Modified safe_query_probability: {description}")
                    
                elif change_type == "add" and "window functions" in description:
                    # Add window functions to the aggressive query method
                    method_pattern = r'(def _generate_aggressive_complex_query\(self\):.*?)(        # Clean up tracking)'
                    match = re.search(method_pattern, modified_content, re.DOTALL)
                    if match:
                        before_cleanup = match.group(1)
                        cleanup_line = match.group(2)
                        new_content = before_cleanup + code_snippet + "\n\n" + cleanup_line
                        modified_content = modified_content.replace(match.group(0), new_content)
                        modifications_applied += 1
                        modification_details.append(f"Added window functions: {description}")
                
                elif change_type == "add" and "division by zero" in description:
                    # Add edge case conditions to _random_condition method
                    method_pattern = r'(        else:  # is_null\n.*?return col\.is_\(exp\.Null\(\)\)\.not_\(\))'
                    match = re.search(method_pattern, modified_content, re.DOTALL)
                    if match:
                        original_else = match.group(1)
                        new_else = original_else + "\n\n" + code_snippet
                        modified_content = modified_content.replace(original_else, new_else)
                        modifications_applied += 1
                        modification_details.append(f"Added edge case conditions: {description}")
                
                elif change_type == "add" and "recursive CTE" in description:
                    # Add recursive CTE method to the class
                    class_end_pattern = r'(\n\ndef random_query_generator\()'
                    match = re.search(class_end_pattern, modified_content)
                    if match:
                        insertion_point = match.start()
                        new_method = "\n" + code_snippet + "\n"
                        modified_content = modified_content[:insertion_point] + new_method + modified_content[insertion_point:]
                        modifications_applied += 1
                        modification_details.append(f"Added recursive CTE method: {description}")
                
                elif change_type == "modify" and "always use aggressive" in description:
                    # Modify generate_complex_query to always use aggressive mode
                    method_pattern = r'(def generate_complex_query\(self\):.*?)(        else:\n.*?return self\._generate_aggressive_complex_query\(\))'
                    match = re.search(method_pattern, modified_content, re.DOTALL)
                    if match:
                        method_start = match.group(1)
                        new_method = method_start + code_snippet
                        modified_content = modified_content.replace(match.group(0), new_method)
                        modifications_applied += 1
                        modification_details.append(f"Modified to always use aggressive mode: {description}")
                
            except Exception as e:
                modification_details.append(f"Failed to apply modification '{description}': {str(e)}")
        
        # Write the modified content back to the file
        if modifications_applied > 0:
            # Create a backup of the original file
            backup_path = target_file_path + f".backup.{int(__import__('time').time())}"
            with open(backup_path, 'w') as f:
                f.write(original_content)
            
            # Write the modified content
            with open(target_file_path, 'w') as f:
                f.write(modified_content)
            
            result = {
                "success": True,
                "modifications_applied": modifications_applied,
                "backup_created": backup_path,
                "details": modification_details,
                "analysis": suggestions.get("analysis", ""),
                "expected_impact": suggestions.get("expected_impact", "")
            }
        else:
            result = {
                "success": False,
                "modifications_applied": 0,
                "error": "No modifications could be applied",
                "details": modification_details
            }
        
        return json.dumps(result, indent=2)
        
    except Exception as e:
        return json.dumps({
            "success": False,
            "error": f"Failed to apply improvements: {str(e)}",
            "modifications_applied": 0
        })


@function_tool
def read_query_generator_code(
    target_file_path: str = "test/crash_fuzzing_agent/tools/random_query_generator_tool.py"
) -> str:
    """
    Read the current query generator code.

    Args:
        target_file_path: Path to the query generator file to read

    Returns:
        The current source code of the query generator
    """
    try:
        if not os.path.exists(target_file_path):
            return f"Error: Target file not found: {target_file_path}"

        with open(target_file_path, 'r') as f:
            content = f.read()

        return content

    except Exception as e:
        return f"Error reading file: {str(e)}"


@function_tool
def backup_and_restore_query_generator(
    action: str,
    backup_path: str = None,
    target_file_path: str = "test/crash_fuzzing_agent/tools/random_query_generator_tool.py"
) -> str:
    """
    Backup or restore the query generator file.
    
    Args:
        action: Either "backup" or "restore"
        backup_path: Path to backup file (required for restore action)
        target_file_path: Path to the query generator file
    
    Returns:
        JSON string containing the result of the backup/restore operation
    """
    try:
        if action == "backup":
            if not os.path.exists(target_file_path):
                return json.dumps({
                    "success": False,
                    "error": f"Target file not found: {target_file_path}"
                })
            
            # Create backup with timestamp
            timestamp = int(__import__('time').time())
            backup_path = f"{target_file_path}.backup.{timestamp}"
            
            with open(target_file_path, 'r') as source:
                content = source.read()
            
            with open(backup_path, 'w') as backup:
                backup.write(content)
            
            return json.dumps({
                "success": True,
                "action": "backup",
                "backup_path": backup_path,
                "message": f"Backup created successfully"
            })
        
        elif action == "restore":
            if not backup_path:
                return json.dumps({
                    "success": False,
                    "error": "backup_path is required for restore action"
                })
            
            if not os.path.exists(backup_path):
                return json.dumps({
                    "success": False,
                    "error": f"Backup file not found: {backup_path}"
                })
            
            with open(backup_path, 'r') as backup:
                content = backup.read()
            
            with open(target_file_path, 'w') as target:
                target.write(content)
            
            return json.dumps({
                "success": True,
                "action": "restore",
                "backup_path": backup_path,
                "message": f"File restored from backup successfully"
            })
        
        else:
            return json.dumps({
                "success": False,
                "error": f"Invalid action: {action}. Must be 'backup' or 'restore'"
            })
    
    except Exception as e:
        return json.dumps({
            "success": False,
            "error": f"Failed to {action} file: {str(e)}"
        })
